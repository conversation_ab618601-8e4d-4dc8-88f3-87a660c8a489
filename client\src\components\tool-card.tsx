import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import type { Tool } from "@shared/schema";

interface ToolCardProps {
  tool: Tool;
}

export default function ToolCard({ tool }: ToolCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string }> = {
      purple: { bg: "bg-purple-100", text: "text-purple-600" },
      green: { bg: "bg-green-100", text: "text-green-600" },
      blue: { bg: "bg-blue-100", text: "text-blue-600" },
      orange: { bg: "bg-orange-100", text: "text-orange-600" },
      indigo: { bg: "bg-indigo-100", text: "text-indigo-600" },
      red: { bg: "bg-red-100", text: "text-red-600" },
    };
    return colorMap[color] || { bg: "bg-gray-100", text: "text-gray-600" };
  };

  const colorClasses = getColorClasses(tool.color);

  return (
    <>
      <Card 
        className="bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer"
        onClick={() => setIsModalOpen(true)}
      >
        <CardContent className="p-6">
          <img 
            src={tool.imageUrl} 
            alt={tool.name}
            className="w-full h-48 object-cover rounded-lg mb-4" 
          />
          <div className="flex items-center space-x-3 mb-3">
            <div className={`w-10 h-10 ${colorClasses.bg} rounded-lg flex items-center justify-center`}>
              <i className={`${tool.icon} ${colorClasses.text}`}></i>
            </div>
            <h3 className="text-xl font-semibold text-slate-900">{tool.name}</h3>
          </div>
          <p className="text-slate-600 mb-4">
            {tool.description}
          </p>
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-500 bg-slate-100 px-2 py-1 rounded capitalize">
              {tool.category}
            </span>
            <button className="text-brand-blue-600 hover:text-brand-blue-700 font-medium text-sm">
              Learn More <i className="fas fa-arrow-right ml-1"></i>
            </button>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-slate-900">
              {tool.name}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <div>
              <span className="inline-block bg-slate-100 text-slate-700 px-3 py-1 rounded-full text-sm font-medium capitalize">
                {tool.category}
              </span>
            </div>
            <p className="text-slate-600 leading-relaxed">{tool.description}</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-slate-900 mb-2">Key Features</h4>
                <ul className="space-y-1 text-slate-600">
                  {tool.features.map((feature, index) => (
                    <li key={index}>• {feature}</li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-slate-900 mb-2">Supported Platforms</h4>
                <ul className="space-y-1 text-slate-600">
                  {tool.platforms.map((platform, index) => (
                    <li key={index}>• {platform}</li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="flex space-x-4 pt-4">
              <Button className="bg-brand-blue-600 hover:bg-brand-blue-700 text-white">
                Try Free
              </Button>
              <Button variant="outline" className="border-slate-300 hover:border-brand-blue-600 text-slate-700 hover:text-brand-blue-600">
                Learn More
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
