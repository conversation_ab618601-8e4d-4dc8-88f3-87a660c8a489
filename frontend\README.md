# Snap2Save Frontend

React frontend for the Snap2Save video downloader application.

## Features

- Download videos from YouTube, Instagram, and Facebook
- Multiple format and quality options
- Modern, responsive UI
- Real-time download progress
- Error handling and user feedback

## Tech Stack

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Radix UI** components
- **TanStack Query** for data fetching
- **React Hook Form** for form handling
- **Wouter** for routing

## Development

1. Install dependencies:
```bash
npm install
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Update the API URL in `.env` if needed:
```
VITE_API_URL=http://localhost:5000
```

4. Start development server:
```bash
npm run dev
```

The app will be available at `http://localhost:3000`

## Building

```bash
npm run build
```

## Deployment on Vercel

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard:
   - `VITE_API_URL`: Your backend URL (e.g., `https://your-backend.onrender.com`)
3. Deploy automatically on push to main branch

## Environment Variables

- `VITE_API_URL`: Backend API URL (required)

## Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── video-downloader.tsx
│   ├── navigation.tsx
│   └── footer.tsx
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── lib/                # Utilities and configurations
├── types/              # TypeScript type definitions
└── main.tsx           # App entry point
```
