
# 🚀 Deployment Guide: Snap2Save

Complete step-by-step guide to deploy your video downloader with frontend on Vercel and backend on Render.

## 📋 Prerequisites Checklist

- [ ] GitHub account
- [ ] Vercel account (free)
- [ ] Render account (free)
- [ ] Code pushed to GitHub repository

## 🎯 Deployment Strategy

```
┌─────────────────┐    API Calls    ┌─────────────────┐
│   Frontend      │ ──────────────► │    Backend      │
│   (Vercel)      │                 │   (Render)      │
│                 │                 │                 │
│ React + Vite    │                 │ Express + yt-dlp│
│ Static Files    │                 │ Video Processing│
└─────────────────┘                 └─────────────────┘
```

## 🔧 Step 1: Prepare Your Repository

### 1.1 Push to GitHub
```bash
# Initialize git if not already done
git init
git add .
git commit -m "Initial commit: Separate frontend and backend"
git branch -M main
git remote add origin https://github.com/yourusername/snap2save.git
git push -u origin main
```

### 1.2 Verify Structure
```
your-repo/
├── frontend/
│   ├── src/
│   ├── package.json
│   ├── vercel.json
│   └── .env.example
├── backend/
│   ├── src/
│   ├── package.json
│   ├── render.yaml
│   └── .env.example
└── README.md
```

## 🌐 Step 2: Deploy Backend to Render

### 2.1 Create Render Account
1. Go to [render.com](https://render.com)
2. Sign up with GitHub
3. Authorize Render to access your repositories

### 2.2 Create Web Service
1. Click **"New +"** → **"Web Service"**
2. Connect your GitHub repository
3. Configure the service:

```yaml
Name: snap2save-backend
Environment: Node
Region: Oregon (US West) or closest to you
Branch: main
Root Directory: backend
Build Command: npm install && pip install yt-dlp && npm run build
Start Command: npm start
```

### 2.3 Set Environment Variables
```
NODE_ENV = production
PORT = 10000
```

### 2.4 Deploy
1. Click **"Create Web Service"**
2. Wait for deployment (5-10 minutes)
3. Note your backend URL: `https://snap2save-backend-xxxx.onrender.com`

### 2.5 Test Backend
```bash
curl https://your-backend-url.onrender.com/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "environment": "production"
}
```

## 🎨 Step 3: Deploy Frontend to Vercel

### 3.1 Create Vercel Account
1. Go to [vercel.com](https://vercel.com)
2. Sign up with GitHub
3. Authorize Vercel to access your repositories

### 3.2 Import Project
1. Click **"Add New..."** → **"Project"**
2. Import your GitHub repository
3. Configure the project:

```yaml
Framework Preset: Vite
Root Directory: frontend
Build Command: npm run build
Output Directory: dist
Install Command: npm install
```

### 3.3 Set Environment Variables
```
VITE_API_URL = https://your-backend-url.onrender.com
```

**Important**: Replace `your-backend-url` with your actual Render URL from Step 2.4

### 3.4 Deploy
1. Click **"Deploy"**
2. Wait for deployment (2-3 minutes)
3. Note your frontend URL: `https://snap2save-xxxx.vercel.app`

## 🔗 Step 4: Connect Frontend and Backend

### 4.1 Update Backend CORS
Edit `backend/src/index.ts`:

```typescript
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? [
        'https://snap2save-xxxx.vercel.app', // Your actual Vercel URL
        'https://your-custom-domain.com',    // If you have a custom domain
      ]
    : ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
}));
```

### 4.2 Commit and Push Changes
```bash
git add .
git commit -m "Update CORS for production deployment"
git push origin main
```

### 4.3 Redeploy Backend
Render will automatically redeploy when you push to main.

## ✅ Step 5: Verify Deployment

### 5.1 Test Complete Flow
1. Visit your Vercel frontend URL
2. Paste a YouTube URL: `https://www.youtube.com/watch?v=dQw4w9WgXcQ`
3. Click "Download"
4. Verify video extraction works
5. Select a format and download

### 5.2 Check Logs
- **Render logs**: Render Dashboard → Your Service → Logs
- **Vercel logs**: Vercel Dashboard → Your Project → Functions

## 🎛️ Step 6: Custom Domains (Optional)

### 6.1 Frontend Custom Domain
1. In Vercel Dashboard → Settings → Domains
2. Add your domain: `snap2save.yourdomain.com`
3. Configure DNS records as shown

### 6.2 Backend Custom Domain
1. In Render Dashboard → Settings → Custom Domains
2. Add your domain: `api.yourdomain.com`
3. Configure DNS records as shown

## 🔧 Step 7: Environment Configuration

### 7.1 Production Environment Variables

**Vercel (Frontend)**:
```env
VITE_API_URL=https://your-backend-url.onrender.com
```

**Render (Backend)**:
```env
NODE_ENV=production
PORT=10000
```

### 7.2 Development Environment Variables

**Frontend** (`.env`):
```env
VITE_API_URL=http://localhost:5000
```

**Backend** (`.env`):
```env
NODE_ENV=development
PORT=5000
```

## 🚨 Troubleshooting

### Common Issues

#### 1. CORS Errors
**Problem**: Frontend can't connect to backend
**Solution**: Update CORS configuration in backend with correct frontend URL

#### 2. yt-dlp Not Found
**Problem**: Backend can't extract videos
**Solution**: Ensure build command includes `pip install yt-dlp`

#### 3. Build Failures
**Problem**: Deployment fails during build
**Solution**: Check Node.js version compatibility (use Node 18+)

#### 4. Environment Variables Not Working
**Problem**: API URL not found
**Solution**: Ensure environment variables are set in deployment platform

### Debug Commands

```bash
# Test backend health
curl https://your-backend-url.onrender.com/health

# Test video extraction
curl -X POST https://your-backend-url.onrender.com/api/extract \
  -H "Content-Type: application/json" \
  -d '{"url":"https://www.youtube.com/watch?v=dQw4w9WgXcQ"}'

# Check frontend build locally
cd frontend && npm run build && npm run preview
```

## 📊 Monitoring

### 7.1 Render Monitoring
- **Metrics**: CPU, Memory, Response time
- **Logs**: Real-time application logs
- **Alerts**: Set up email notifications

### 7.2 Vercel Analytics
- **Performance**: Core Web Vitals
- **Usage**: Page views, unique visitors
- **Errors**: Runtime errors and build failures

## 🎉 Success!

Your video downloader is now live:

- **Frontend**: `https://your-app.vercel.app`
- **Backend**: `https://your-backend.onrender.com`
- **Health Check**: `https://your-backend.onrender.com/health`

## 📈 Next Steps

1. **Custom Domain**: Set up your own domain
2. **Analytics**: Add Google Analytics or Vercel Analytics
3. **Monitoring**: Set up error tracking (Sentry)
4. **CDN**: Optimize with Vercel's global CDN
5. **Database**: Add persistent storage if needed

---

**🚀 Congratulations! Your video downloader is now deployed and ready for users!**
