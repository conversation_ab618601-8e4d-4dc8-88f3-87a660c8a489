import { useParams } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Navigation from "@/components/navigation";
import Footer from "@/components/footer";
import type { Tool } from "@shared/schema";

export default function ToolDetails() {
  const { id } = useParams<{ id: string }>();
  
  const { data: tool, isLoading, error } = useQuery<Tool>({
    queryKey: [`/api/tools/${id}`],
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <div className="min-h-screen">
        <Navigation />
        <div className="pt-32 pb-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-blue-600 mx-auto"></div>
              <p className="mt-4 text-slate-600">Loading tool details...</p>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !tool) {
    return (
      <div className="min-h-screen">
        <Navigation />
        <div className="pt-32 pb-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <Card>
              <CardContent className="pt-6 text-center">
                <i className="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h1 className="text-2xl font-bold text-slate-900 mb-2">Tool Not Found</h1>
                <p className="text-slate-600 mb-6">The tool you're looking for doesn't exist or has been removed.</p>
                <Link href="/">
                  <Button>Back to Home</Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string }> = {
      purple: { bg: "bg-purple-100", text: "text-purple-600" },
      green: { bg: "bg-green-100", text: "text-green-600" },
      blue: { bg: "bg-blue-100", text: "text-blue-600" },
      orange: { bg: "bg-orange-100", text: "text-orange-600" },
      indigo: { bg: "bg-indigo-100", text: "text-indigo-600" },
      red: { bg: "bg-red-100", text: "text-red-600" },
    };
    return colorMap[color] || { bg: "bg-gray-100", text: "text-gray-600" };
  };

  const colorClasses = getColorClasses(tool.color);

  return (
    <div className="min-h-screen">
      <Navigation />
      <div className="pt-32 pb-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <Link href="/">
              <Button variant="ghost" className="text-brand-blue-600 hover:text-brand-blue-700">
                <i className="fas fa-arrow-left mr-2"></i>
                Back to Tools
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <img 
                src={tool.imageUrl} 
                alt={tool.name}
                className="w-full h-64 object-cover rounded-xl shadow-lg mb-6" 
              />
              
              <div className="flex items-center space-x-4 mb-6">
                <div className={`w-16 h-16 ${colorClasses.bg} rounded-xl flex items-center justify-center`}>
                  <i className={`${tool.icon} ${colorClasses.text} text-2xl`}></i>
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-slate-900">{tool.name}</h1>
                  <span className="inline-block bg-slate-100 text-slate-700 px-3 py-1 rounded-full text-sm font-medium capitalize mt-2">
                    {tool.category}
                  </span>
                </div>
              </div>

              <p className="text-lg text-slate-600 leading-relaxed mb-8">
                {tool.description}
              </p>

              <div className="flex space-x-4">
                <Button size="lg" className="bg-brand-blue-600 hover:bg-brand-blue-700 text-white">
                  Start Free Trial
                </Button>
                <Button size="lg" variant="outline" className="border-slate-300 hover:border-brand-blue-600 text-slate-700 hover:text-brand-blue-600">
                  Schedule Demo
                </Button>
              </div>
            </div>

            <div className="space-y-8">
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">Key Features</h3>
                  <ul className="space-y-3">
                    {tool.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <i className="fas fa-check-circle text-green-500 mt-1"></i>
                        <span className="text-slate-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">Supported Platforms</h3>
                  <ul className="space-y-3">
                    {tool.platforms.map((platform, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <i className="fas fa-plug text-brand-blue-500 mt-1"></i>
                        <span className="text-slate-600">{platform}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-slate-50">
                <CardContent className="pt-6">
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">Why Choose This Tool?</h3>
                  <ul className="space-y-2 text-slate-600">
                    <li>• Easy setup and configuration</li>
                    <li>• 24/7 customer support</li>
                    <li>• Enterprise-grade security</li>
                    <li>• Regular updates and improvements</li>
                    <li>• Comprehensive documentation</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
