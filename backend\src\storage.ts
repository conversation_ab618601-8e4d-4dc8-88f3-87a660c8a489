import { Video, VideoFormat, VideoWithFormats } from "./types/index.js";

export interface IStorage {
  saveVideo(video: Omit<Video, 'id' | 'extractedAt'>, formats: Omit<VideoFormat, 'id' | 'videoId'>[]): Promise<VideoWithFormats>;
  getVideoByUrl(url: string): Promise<VideoWithFormats | undefined>;
}

export class MemStorage implements IStorage {
  private videos: Map<number, Video>;
  private videoFormats: Map<number, VideoFormat>;
  private currentVideoId: number;
  private currentFormatId: number;

  constructor() {
    this.videos = new Map();
    this.videoFormats = new Map();
    this.currentVideoId = 1;
    this.currentFormatId = 1;
  }

  async saveVideo(insertVideo: Omit<Video, 'id' | 'extractedAt'>, insertFormats: Omit<VideoFormat, 'id' | 'videoId'>[]): Promise<VideoWithFormats> {
    const videoId = this.currentVideoId++;
    const video: Video = { 
      ...insertVideo, 
      id: videoId,
      extractedAt: new Date()
    };
    this.videos.set(videoId, video);

    const formats: VideoFormat[] = insertFormats.map(format => {
      const formatId = this.currentFormatId++;
      const videoFormat: VideoFormat = {
        id: formatId,
        videoId,
        formatId: format.formatId,
        ext: format.ext,
        quality: format.quality,
        filesize: format.filesize ?? null,
        resolution: format.resolution ?? null,
        fps: format.fps ?? null,
        vcodec: format.vcodec ?? null,
        acodec: format.acodec ?? null
      };
      this.videoFormats.set(formatId, videoFormat);
      return videoFormat;
    });

    return {
      ...video,
      formats
    };
  }

  async getVideoByUrl(url: string): Promise<VideoWithFormats | undefined> {
    const video = Array.from(this.videos.values()).find(v => v.url === url);
    if (!video) return undefined;

    const formats = Array.from(this.videoFormats.values()).filter(
      format => format.videoId === video.id
    );

    return {
      ...video,
      formats
    };
  }
}

export const storage = new MemStorage();
