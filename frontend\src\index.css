@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* Brand colors */
  --brand-blue-50: hsl(214, 100%, 97%);
  --brand-blue-100: hsl(214, 95%, 93%);
  --brand-blue-200: hsl(213, 97%, 87%);
  --brand-blue-300: hsl(212, 96%, 78%);
  --brand-blue-400: hsl(213, 94%, 68%);
  --brand-blue-500: hsl(217, 91%, 60%);
  --brand-blue-600: hsl(221, 83%, 53%);
  --brand-blue-700: hsl(224, 76%, 48%);
  --brand-blue-800: hsl(226, 71%, 40%);
  --brand-blue-900: hsl(224, 64%, 33%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

@layer utilities {
  .text-brand-blue-400 {
    color: hsl(var(--brand-blue-400));
  }
  .text-brand-blue-500 {
    color: hsl(var(--brand-blue-500));
  }
  .text-brand-blue-600 {
    color: hsl(var(--brand-blue-600));
  }
  .text-brand-blue-700 {
    color: hsl(var(--brand-blue-700));
  }
  .bg-brand-blue-50 {
    background-color: hsl(var(--brand-blue-50));
  }
  .bg-brand-blue-100 {
    background-color: hsl(var(--brand-blue-100));
  }
  .bg-brand-blue-600 {
    background-color: hsl(var(--brand-blue-600));
  }
  .bg-brand-blue-700 {
    background-color: hsl(var(--brand-blue-700));
  }
  .border-brand-blue-600 {
    border-color: hsl(var(--brand-blue-600));
  }
  .hover\:bg-brand-blue-700:hover {
    background-color: hsl(var(--brand-blue-700));
  }
  .hover\:text-brand-blue-600:hover {
    color: hsl(var(--brand-blue-600));
  }
  .hover\:border-brand-blue-600:hover {
    border-color: hsl(var(--brand-blue-600));
  }
}
