import type { Express } from "express";
import { createServer, type Server } from "http";
import { spawn } from "child_process";
import { storage } from "./storage";
import { videoExtractSchema, downloadRequestSchema, type VideoWithFormats } from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Extract video metadata
  app.post("/api/extract", async (req, res) => {
    try {
      const { url } = videoExtractSchema.parse(req.body);

      // Check if video already exists in storage
      const existingVideo = await storage.getVideoByUrl(url);
      if (existingVideo) {
        return res.json(existingVideo);
      }

      // Extract video info using yt-dlp
      const videoInfo = await extractVideoInfo(url);
      
      // Save to storage
      const savedVideo = await storage.saveVideo(
        {
          url,
          title: videoInfo.title,
          thumbnail: videoInfo.thumbnail,
          duration: videoInfo.duration,
          platform: getPlatform(url)
        },
        videoInfo.formats
      );

      res.json(savedVideo);
    } catch (error) {
      console.error("Extract error:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to extract video info" 
      });
    }
  });

  // Download video
  app.post("/api/download", async (req, res) => {
    try {
      const { url, formatId } = downloadRequestSchema.parse(req.body);

      // Get video info to find the title for filename
      const videoInfo = await storage.getVideoByUrl(url);
      if (!videoInfo) {
        return res.status(404).json({ message: "Video not found" });
      }

      const selectedFormat = videoInfo.formats.find(f => f.formatId === formatId);
      if (!selectedFormat) {
        return res.status(404).json({ message: "Format not found" });
      }

      // Generate safe filename
      const safeTitle = videoInfo.title.replace(/[^a-zA-Z0-9\s-_]/g, '').substring(0, 100);
      const filename = `${safeTitle}.${selectedFormat.ext}`;

      // Set response headers for download
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Transfer-Encoding', 'chunked');

      // Stream video using yt-dlp
      const ytdlp = spawn('yt-dlp', [
        '--format', formatId,
        '--output', '-',
        url
      ]);

      ytdlp.stdout.pipe(res);

      ytdlp.stderr.on('data', (data) => {
        console.error('yt-dlp stderr:', data.toString());
      });

      ytdlp.on('close', (code) => {
        if (code !== 0) {
          console.error(`yt-dlp process exited with code ${code}`);
        }
        res.end();
      });

      ytdlp.on('error', (error) => {
        console.error('yt-dlp error:', error);
        if (!res.headersSent) {
          res.status(500).json({ message: 'Download failed' });
        }
      });

    } catch (error) {
      console.error("Download error:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to download video" 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}

function getPlatform(url: string): string {
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return 'youtube';
  } else if (url.includes('instagram.com')) {
    return 'instagram';
  } else if (url.includes('facebook.com') || url.includes('fb.watch')) {
    return 'facebook';
  }
  return 'unknown';
}

async function extractVideoInfo(url: string): Promise<{
  title: string;
  thumbnail: string;
  duration: number;
  formats: Array<{
    formatId: string;
    ext: string;
    quality: string;
    filesize: number | null;
    resolution: string | null;
    fps: number | null;
    vcodec: string | null;
    acodec: string | null;
  }>;
}> {
  return new Promise((resolve, reject) => {
    const ytdlp = spawn('yt-dlp', [
      '--dump-json',
      '--no-playlist',
      url
    ]);

    let jsonOutput = '';
    let errorOutput = '';

    ytdlp.stdout.on('data', (data) => {
      jsonOutput += data.toString();
    });

    ytdlp.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    ytdlp.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Failed to extract video info: ${errorOutput}`));
        return;
      }

      try {
        const videoData = JSON.parse(jsonOutput);
        
        const formats = (videoData.formats || [])
          .filter((f: any) => f.vcodec && f.vcodec !== 'none')
          .map((f: any) => ({
            formatId: f.format_id,
            ext: f.ext || 'mp4',
            quality: f.format_note || f.quality || 'unknown',
            filesize: f.filesize || null,
            resolution: f.resolution || null,
            fps: f.fps || null,
            vcodec: f.vcodec || null,
            acodec: f.acodec || null
          }));

        resolve({
          title: videoData.title || 'Unknown Title',
          thumbnail: videoData.thumbnail || '',
          duration: videoData.duration || 0,
          formats
        });
      } catch (error) {
        reject(new Error(`Failed to parse video info: ${error}`));
      }
    });

    ytdlp.on('error', (error) => {
      reject(new Error(`yt-dlp process error: ${error.message}`));
    });
  });
}
