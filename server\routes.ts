import type { Express } from "express";
import { createServer, type Server } from "http";
import { spawn } from "child_process";
import { storage } from "./storage";
import { videoExtractSchema, downloadRequestSchema, type VideoWithFormats } from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Extract video metadata
  app.post("/api/extract", async (req, res) => {
    try {
      const { url } = videoExtractSchema.parse(req.body);

      // Check if video already exists in storage
      const existingVideo = await storage.getVideoByUrl(url);
      if (existingVideo) {
        return res.json(existingVideo);
      }

      // Extract video info using multiple methods
      const videoInfo = await extractVideoInfo(url);
      
      // Save to storage
      const savedVideo = await storage.saveVideo(
        {
          url,
          title: videoInfo.title,
          thumbnail: videoInfo.thumbnail,
          duration: videoInfo.duration,
          platform: getPlatform(url)
        },
        videoInfo.formats
      );

      res.json(savedVideo);
    } catch (error) {
      console.error("Extract error:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to extract video info" 
      });
    }
  });

  // Download video
  app.post("/api/download", async (req, res) => {
    try {
      const { url, formatId } = downloadRequestSchema.parse(req.body);

      // Get video info to find the title for filename
      const videoInfo = await storage.getVideoByUrl(url);
      if (!videoInfo) {
        return res.status(404).json({ message: "Video not found" });
      }

      const selectedFormat = videoInfo.formats.find(f => f.formatId === formatId);
      if (!selectedFormat) {
        return res.status(404).json({ message: "Format not found" });
      }

      // Generate safe filename
      const safeTitle = videoInfo.title.replace(/[^a-zA-Z0-9\s-_]/g, '').substring(0, 100);
      const filename = `${safeTitle}.${selectedFormat.ext}`;

      // Set response headers for download
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Transfer-Encoding', 'chunked');

      // Try yt-dlp first, then fallback to alternative methods
      try {
        await downloadWithYtDlp(url, formatId, res);
      } catch (error) {
        console.log('yt-dlp download failed, trying alternative method:', error);
        await downloadAlternative(url, formatId, res, videoInfo);
      }

    } catch (error) {
      console.error("Download error:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to download video" 
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}

function getPlatform(url: string): string {
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return 'youtube';
  } else if (url.includes('instagram.com')) {
    return 'instagram';
  } else if (url.includes('facebook.com') || url.includes('fb.watch')) {
    return 'facebook';
  } else if (url.includes('pw.live')) {
    return 'pwlive';
  }
  return 'unknown';
}

async function extractVideoInfo(url: string): Promise<{
  title: string;
  thumbnail: string;
  duration: number;
  formats: Array<{
    formatId: string;
    ext: string;
    quality: string;
    filesize: number | null;
    resolution: string | null;
    fps: number | null;
    vcodec: string | null;
    acodec: string | null;
  }>;
}> {
  const platform = getPlatform(url);

  // Try yt-dlp first, then fallback to platform-specific methods
  try {
    return await extractWithYtDlp(url);
  } catch (error) {
    console.log('yt-dlp failed, trying alternative methods:', error);

    switch (platform) {
      case 'instagram':
        return await extractInstagramInfo(url);
      case 'youtube':
        return await extractYouTubeInfo(url);
      case 'facebook':
        return await extractFacebookInfo(url);
      case 'pwlive':
        return await extractPwLiveInfo(url);
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }
}

async function extractWithYtDlp(url: string): Promise<any> {
  return new Promise((resolve, reject) => {
    const ytdlp = spawn('python', [
      '-m', 'yt_dlp',
      '--dump-json',
      '--no-playlist',
      url
    ]);

    let jsonOutput = '';
    let errorOutput = '';

    ytdlp.stdout.on('data', (data) => {
      jsonOutput += data.toString();
    });

    ytdlp.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    ytdlp.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Failed to extract video info: ${errorOutput}`));
        return;
      }

      try {
        const videoData = JSON.parse(jsonOutput);

        const formats = (videoData.formats || [])
          .filter((f: any) => {
            // Prioritize formats with both video and audio
            return f.vcodec && f.vcodec !== 'none' && f.acodec && f.acodec !== 'none';
          })
          .map((f: any) => ({
            formatId: f.format_id,
            ext: f.ext || 'mp4',
            quality: f.format_note || f.quality || 'unknown',
            filesize: f.filesize || null,
            resolution: f.resolution || null,
            fps: f.fps || null,
            vcodec: f.vcodec || null,
            acodec: f.acodec || null
          }));

        // If no formats with both video and audio, add some common combined formats
        if (formats.length === 0) {
          formats.push(
            {
              formatId: 'best[height<=720]',
              ext: 'mp4',
              quality: 'Best 720p with audio',
              filesize: null,
              resolution: '720p',
              fps: null,
              vcodec: 'h264',
              acodec: 'aac'
            },
            {
              formatId: 'best[height<=480]',
              ext: 'mp4',
              quality: 'Best 480p with audio',
              filesize: null,
              resolution: '480p',
              fps: null,
              vcodec: 'h264',
              acodec: 'aac'
            },
            {
              formatId: 'best',
              ext: 'mp4',
              quality: 'Best quality with audio',
              filesize: null,
              resolution: 'Best',
              fps: null,
              vcodec: 'h264',
              acodec: 'aac'
            }
          );
        }

        resolve({
          title: videoData.title || 'Unknown Title',
          thumbnail: videoData.thumbnail || '',
          duration: videoData.duration || 0,
          formats
        });
      } catch (error) {
        reject(new Error(`Failed to parse video info: ${error}`));
      }
    });

    ytdlp.on('error', (error) => {
      reject(new Error(`yt-dlp process error: ${error.message}`));
    });
  });
}

async function extractInstagramInfo(url: string): Promise<any> {
  // For now, provide a mock response for Instagram
  // In production, you'd use a proper Instagram API or service
  try {
    // Extract shortcode from URL
    const shortcodeMatch = url.match(/\/p\/([A-Za-z0-9_-]+)/);
    if (!shortcodeMatch) {
      throw new Error('Invalid Instagram URL format');
    }

    const shortcode = shortcodeMatch[1];

    // Mock data for demonstration
    return {
      title: `Instagram Post ${shortcode}`,
      thumbnail: 'https://via.placeholder.com/640x640?text=Instagram+Video',
      duration: 30,
      formats: [
        {
          formatId: 'video_hd',
          ext: 'mp4',
          quality: 'HD',
          filesize: 5242880, // 5MB
          resolution: '720x720',
          fps: 30,
          vcodec: 'h264',
          acodec: 'aac'
        },
        {
          formatId: 'video_sd',
          ext: 'mp4',
          quality: 'SD',
          filesize: 2621440, // 2.5MB
          resolution: '480x480',
          fps: 30,
          vcodec: 'h264',
          acodec: 'aac'
        }
      ]
    };
  } catch (error) {
    throw new Error(`Instagram extraction failed: ${error}`);
  }
}

async function extractYouTubeInfo(url: string): Promise<any> {
  // YouTube extraction - simplified version
  // In production, you'd want to use YouTube Data API
  throw new Error('YouTube extraction not implemented yet. Please install yt-dlp.');
}

async function extractFacebookInfo(url: string): Promise<any> {
  // Facebook extraction - simplified version
  throw new Error('Facebook extraction not implemented yet. Please install yt-dlp.');
}

async function extractPwLiveInfo(url: string): Promise<any> {
  try {
    // Parse PW Live URL parameters
    const urlObj = new URL(url);
    const params = urlObj.searchParams;

    const batchSlug = params.get('batchSlug');
    const scheduleId = params.get('scheduleId');
    const subjectSlug = params.get('subjectSlug');
    const topicSlug = params.get('topicSlug');

    if (!batchSlug || !scheduleId) {
      throw new Error('Invalid PW Live URL: missing required parameters');
    }

    // Try to extract video information from PW Live API
    const videoInfo = await extractPwLiveVideoInfo(batchSlug, scheduleId, params);

    return videoInfo;
  } catch (error) {
    console.error('PW Live extraction error:', error);
    throw new Error(`PW Live extraction failed: ${error}`);
  }
}

async function extractPwLiveVideoInfo(batchSlug: string, scheduleId: string, params: URLSearchParams): Promise<any> {
  try {
    // PW Live uses a complex API structure, let's try to fetch video metadata
    const apiUrl = `https://api.pw.live/api/v2/batches/${batchSlug}/lectures/${scheduleId}`;

    const response = await fetch(apiUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
        'Referer': 'https://www.pw.live/',
        'Origin': 'https://www.pw.live'
      }
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }

    const data = await response.json();

    // Extract video information from API response
    const lecture = data.data || data;
    const videoTitle = lecture.topic || lecture.title || `PW Live Lecture - ${scheduleId}`;
    const thumbnail = lecture.thumbnail || lecture.image || 'https://via.placeholder.com/640x360?text=PW+Live+Video';
    const duration = lecture.duration || 3600; // Default 1 hour

    // Try to find video URLs in the response
    const videoUrls = extractVideoUrls(lecture);

    const formats = videoUrls.map((videoUrl, index) => ({
      formatId: `pwlive_${index}`,
      ext: 'mp4',
      quality: index === 0 ? 'HD' : 'SD',
      filesize: null,
      resolution: index === 0 ? '720p' : '480p',
      fps: 30,
      vcodec: 'h264',
      acodec: 'aac',
      url: videoUrl // Store the actual video URL
    }));

    // If no video URLs found, provide fallback
    if (formats.length === 0) {
      formats.push({
        formatId: 'pwlive_fallback',
        ext: 'mp4',
        quality: 'HD',
        filesize: null,
        resolution: '720p',
        fps: 30,
        vcodec: 'h264',
        acodec: 'aac',
        url: null
      });
    }

    return {
      title: videoTitle,
      thumbnail: thumbnail,
      duration: duration,
      formats: formats,
      originalUrl: `https://www.pw.live/watch/?batchSlug=${batchSlug}&scheduleId=${scheduleId}`,
      metadata: {
        batchSlug,
        scheduleId,
        subject: params.get('subjectSlug'),
        topic: params.get('topicSlug')
      }
    };

  } catch (error) {
    console.error('PW Live API extraction failed:', error);

    // Fallback with extracted parameters
    return {
      title: `PW Live Lecture - ${scheduleId.substring(0, 8)}`,
      thumbnail: 'https://via.placeholder.com/640x360?text=PW+Live+Video',
      duration: 3600,
      formats: [
        {
          formatId: 'pwlive_hd',
          ext: 'mp4',
          quality: 'HD',
          filesize: null,
          resolution: '720p',
          fps: 30,
          vcodec: 'h264',
          acodec: 'aac',
          url: null
        }
      ],
      originalUrl: `https://www.pw.live/watch/?batchSlug=${batchSlug}&scheduleId=${scheduleId}`,
      metadata: {
        batchSlug,
        scheduleId,
        subject: params.get('subjectSlug'),
        topic: params.get('topicSlug')
      }
    };
  }
}

function extractVideoUrls(lectureData: any): string[] {
  const urls: string[] = [];

  // Common patterns for video URLs in PW Live responses
  const possiblePaths = [
    'videoUrl',
    'video_url',
    'streamUrl',
    'stream_url',
    'hlsUrl',
    'hls_url',
    'manifestUrl',
    'manifest_url',
    'sources',
    'videoSources',
    'video_sources'
  ];

  function searchForUrls(obj: any, depth = 0): void {
    if (depth > 5) return; // Prevent infinite recursion

    if (typeof obj === 'string' && (obj.includes('.m3u8') || obj.includes('.mp4') || obj.includes('video'))) {
      if (obj.startsWith('http')) {
        urls.push(obj);
      }
    } else if (Array.isArray(obj)) {
      obj.forEach(item => searchForUrls(item, depth + 1));
    } else if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        if (possiblePaths.includes(key.toLowerCase())) {
          searchForUrls(obj[key], depth + 1);
        } else {
          searchForUrls(obj[key], depth + 1);
        }
      });
    }
  }

  searchForUrls(lectureData);

  // Remove duplicates and filter valid URLs
  return [...new Set(urls)].filter(url =>
    url.startsWith('http') && (url.includes('.m3u8') || url.includes('.mp4'))
  );
}

async function downloadWithYtDlp(url: string, formatId: string, res: any): Promise<void> {
  return new Promise((resolve, reject) => {
    // Ensure we get both video and audio by using format selection that merges them
    let format = formatId;

    // If it's a simple format ID, make sure we get audio too
    if (!formatId.includes('[') && !formatId.includes('best') && !formatId.includes('worst')) {
      format = `${formatId}+bestaudio/best[height<=720]/best`;
    }

    const ytdlp = spawn('python', [
      '-m', 'yt_dlp',
      '--format', format,
      '--merge-output-format', 'mp4',
      '--output', '-',
      url
    ]);

    ytdlp.stdout.pipe(res);

    ytdlp.stderr.on('data', (data) => {
      console.error('yt-dlp stderr:', data.toString());
    });

    ytdlp.on('close', (code) => {
      if (code !== 0) {
        console.error(`yt-dlp process exited with code ${code}`);
        reject(new Error(`yt-dlp process exited with code ${code}`));
      } else {
        res.end();
        resolve();
      }
    });

    ytdlp.on('error', (error) => {
      console.error('yt-dlp error:', error);
      reject(error);
    });
  });
}

async function downloadAlternative(url: string, formatId: string, res: any, videoInfo: any): Promise<void> {
  const platform = getPlatform(url);

  if (platform === 'pwlive') {
    await downloadPwLiveVideo(url, formatId, res, videoInfo);
  } else if (platform === 'instagram') {
    // Create a simple text file as a demo download for Instagram
    const content = `Instagram Video Download Demo\n\nURL: ${url}\nFormat: ${formatId}\nTitle: ${videoInfo.title}\n\nThis is a demo file. In production, this would be the actual video content.`;

    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Length', Buffer.byteLength(content));
    res.write(content);
    res.end();
  } else {
    throw new Error(`Alternative download not implemented for ${platform}`);
  }
}

async function downloadPwLiveVideo(url: string, formatId: string, res: any, videoInfo: any): Promise<void> {
  try {
    // Find the selected format
    const selectedFormat = videoInfo.formats.find((f: any) => f.formatId === formatId);
    if (!selectedFormat) {
      throw new Error('Selected format not found');
    }

    // If we have a direct video URL, stream it
    if (selectedFormat.url) {
      await streamVideoFromUrl(selectedFormat.url, res, videoInfo.title);
    } else {
      // Try to get video URL using the metadata
      const videoUrl = await getPwLiveVideoUrl(videoInfo.metadata);
      if (videoUrl) {
        await streamVideoFromUrl(videoUrl, res, videoInfo.title);
      } else {
        throw new Error('Could not find video stream URL');
      }
    }
  } catch (error) {
    console.error('PW Live download error:', error);

    // Fallback: provide information file with instructions
    const content = `PW Live Video Information\n\nTitle: ${videoInfo.title}\nURL: ${url}\nFormat: ${formatId}\n\nNote: This video requires authentication or special access.\nPlease try accessing the video directly through the PW Live website.\n\nOriginal URL: ${videoInfo.originalUrl || url}\n\nTo download this video:\n1. Log in to your PW Live account\n2. Navigate to the video page\n3. Use browser developer tools to find the video stream URL\n4. Use a tool like yt-dlp with proper authentication`;

    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', `attachment; filename="${videoInfo.title.replace(/[^a-zA-Z0-9\s-_]/g, '')}.txt"`);
    res.setHeader('Content-Length', Buffer.byteLength(content));
    res.write(content);
    res.end();
  }
}

async function streamVideoFromUrl(videoUrl: string, res: any, title: string): Promise<void> {
  try {
    const response = await fetch(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.pw.live/',
        'Origin': 'https://www.pw.live'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch video: ${response.status}`);
    }

    // Set appropriate headers for video download
    const contentType = response.headers.get('content-type') || 'video/mp4';
    const contentLength = response.headers.get('content-length');

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${title.replace(/[^a-zA-Z0-9\s-_]/g, '')}.mp4"`);

    if (contentLength) {
      res.setHeader('Content-Length', contentLength);
    }

    // Stream the video data
    if (response.body) {
      const reader = response.body.getReader();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          res.write(Buffer.from(value));
        }
      } finally {
        reader.releaseLock();
      }
    }

    res.end();
  } catch (error) {
    throw new Error(`Video streaming failed: ${error}`);
  }
}

async function getPwLiveVideoUrl(metadata: any): Promise<string | null> {
  try {
    // Try different API endpoints to get video URL
    const { batchSlug, scheduleId } = metadata;

    // Try the video stream API
    const streamApiUrl = `https://api.pw.live/api/v2/batches/${batchSlug}/lectures/${scheduleId}/stream`;

    const response = await fetch(streamApiUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
        'Referer': 'https://www.pw.live/',
        'Origin': 'https://www.pw.live'
      }
    });

    if (response.ok) {
      const data = await response.json();
      const videoUrls = extractVideoUrls(data);
      return videoUrls[0] || null;
    }

    return null;
  } catch (error) {
    console.error('Failed to get PW Live video URL:', error);
    return null;
  }
}
