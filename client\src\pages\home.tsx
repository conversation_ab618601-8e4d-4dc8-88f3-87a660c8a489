import Navigation from "@/components/navigation";
import VideoDownloader from "@/components/video-downloader";
import Footer from "@/components/footer";

export default function Home() {
  return (
    <div className="min-h-screen bg-slate-50">
      <Navigation />
      <main className="pt-24 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-6">
              Video Downloader
            </h1>
            <p className="text-xl text-slate-600 mb-8 leading-relaxed max-w-3xl mx-auto">
              Download videos from YouTube, Instagram, Facebook, and PW Live in multiple formats
            </p>
          </div>
          
          <VideoDownloader />
          
          <div className="mt-16 text-center">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="fab fa-youtube text-2xl"></i>
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">YouTube</h3>
                <p className="text-slate-600">
                  Download videos in HD, 4K, and various formats from YouTube
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-pink-100 text-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="fab fa-instagram text-2xl"></i>
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Instagram</h3>
                <p className="text-slate-600">
                  Save Instagram videos, reels, and stories to your device
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="fab fa-facebook text-2xl"></i>
                </div>
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Facebook</h3>
                <p className="text-slate-600">
                  Download Facebook videos in original quality
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
