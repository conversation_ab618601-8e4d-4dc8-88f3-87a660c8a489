import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { videoExtractSchema, type VideoExtractRequest, type VideoWithFormats, type DownloadRequest } from "@shared/schema";

export default function VideoDownloader() {
  const [videoData, setVideoData] = useState<VideoWithFormats | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<string>("");
  const { toast } = useToast();

  const form = useForm<VideoExtractRequest>({
    resolver: zodResolver(videoExtractSchema),
    defaultValues: {
      url: "",
    },
  });

  const extractMutation = useMutation({
    mutationFn: async (data: VideoExtractRequest) => {
      const response = await fetch("/api/extract", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to extract video");
      }
      
      return response.json();
    },
    onSuccess: (data: VideoWithFormats) => {
      setVideoData(data);
      setSelectedFormat(data.formats[0]?.formatId || "");
      toast({
        title: "Video extracted successfully",
        description: `Found ${data.formats.length} available formats`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Extraction failed",
        description: error.message || "Failed to extract video information",
        variant: "destructive",
      });
    },
  });

  const downloadMutation = useMutation({
    mutationFn: async (data: DownloadRequest) => {
      const response = await fetch("/api/download", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Download failed");
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = getFilename();
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    },
    onSuccess: () => {
      toast({
        title: "Download started",
        description: "Your video download should begin shortly",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Download failed",
        description: error.message || "Failed to download video",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: VideoExtractRequest) => {
    extractMutation.mutate(data);
  };

  const handleDownload = () => {
    if (!videoData || !selectedFormat) return;
    
    downloadMutation.mutate({
      url: videoData.url,
      formatId: selectedFormat,
    });
  };

  const getFilename = () => {
    if (!videoData) return "video.mp4";
    const format = videoData.formats.find(f => f.formatId === selectedFormat);
    const safeTitle = videoData.title.replace(/[^a-zA-Z0-9\s-_]/g, '').substring(0, 50);
    return `${safeTitle}.${format?.ext || 'mp4'}`;
  };

  const formatFileSize = (bytes: number | null) => {
    if (!bytes) return "Unknown size";
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'youtube':
        return 'fab fa-youtube';
      case 'instagram':
        return 'fab fa-instagram';
      case 'facebook':
        return 'fab fa-facebook';
      case 'pwlive':
        return 'fas fa-play-circle';
      default:
        return 'fas fa-video';
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="bg-white rounded-2xl shadow-xl p-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="relative">
              <Input
                placeholder="📋 Paste YouTube, Instagram, Facebook, or PW Live URL here..."
                {...form.register("url")}
                disabled={extractMutation.isPending}
                className="h-14 text-lg pl-4 pr-20 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:ring-0"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-2 top-2 h-10 px-3 text-gray-500 hover:text-gray-700"
                onClick={() => form.setValue("url", "")}
              >
                ✖ Clear
              </Button>
            </div>

            <Button
              type="submit"
              disabled={extractMutation.isPending || !form.watch("url")}
              size="lg"
              className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
            >
              {extractMutation.isPending ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  Download Processing...
                </>
              ) : (
                <>
                  Download
                </>
              )}
            </Button>

            {form.formState.errors.url && (
              <div className="text-red-500 text-center">
                ⚠️ Invalid Instagram link. Please check and try again.
              </div>
            )}
          </form>
        </Form>
      </div>

      {videoData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <i className={`${getPlatformIcon(videoData.platform)} text-2xl text-brand-blue-600`}></i>
              <div>
                <h3 className="text-lg font-semibold">{videoData.title}</h3>
                <Badge variant="outline" className="mt-1 capitalize">
                  {videoData.platform}
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {videoData.thumbnail && (
              <div className="aspect-video rounded-lg overflow-hidden bg-slate-100">
                <img 
                  src={videoData.thumbnail} 
                  alt={videoData.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Select Format & Quality
              </label>
              <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose format..." />
                </SelectTrigger>
                <SelectContent>
                  {videoData.formats.map((format) => (
                    <SelectItem key={format.formatId} value={format.formatId}>
                      <div className="flex items-center justify-between w-full">
                        <span>
                          {format.quality} ({format.ext.toUpperCase()})
                        </span>
                        <span className="text-sm text-slate-500 ml-4">
                          {format.resolution && `${format.resolution} • `}
                          {formatFileSize(format.filesize)}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-slate-50 p-3 rounded-lg">
                <div className="font-medium text-slate-700">Duration</div>
                <div className="text-slate-600">
                  {videoData.duration ? `${Math.floor(videoData.duration / 60)}:${(videoData.duration % 60).toString().padStart(2, '0')}` : 'Unknown'}
                </div>
              </div>
              <div className="bg-slate-50 p-3 rounded-lg">
                <div className="font-medium text-slate-700">Platform</div>
                <div className="text-slate-600 capitalize">{videoData.platform}</div>
              </div>
              <div className="bg-slate-50 p-3 rounded-lg">
                <div className="font-medium text-slate-700">Formats Available</div>
                <div className="text-slate-600">{videoData.formats.length} options</div>
              </div>
            </div>

            <Button 
              onClick={handleDownload}
              disabled={!selectedFormat || downloadMutation.isPending}
              size="lg"
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {downloadMutation.isPending ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  Downloading...
                </>
              ) : (
                <>
                  <i className="fas fa-download mr-2"></i>
                  Download Video
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}