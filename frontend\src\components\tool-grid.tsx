import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import ToolCard from "./tool-card";
import type { Tool } from "@shared/schema";

export default function ToolGrid() {
  const [activeFilter, setActiveFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  const { data: tools = [], isLoading } = useQuery<Tool[]>({
    queryKey: ["/api/tools"],
  });

  const filteredTools = useMemo(() => {
    let result = tools;

    // Apply category filter
    if (activeFilter !== "all") {
      result = result.filter(tool => tool.category === activeFilter);
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(tool => 
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query) ||
        tool.category.toLowerCase().includes(query)
      );
    }

    return result;
  }, [tools, activeFilter, searchQuery]);

  const categories = [
    { id: "all", label: "All Tools" },
    { id: "automation", label: "Automation" },
    { id: "integration", label: "Integration" },
    { id: "productivity", label: "Productivity" },
    { id: "analytics", label: "Analytics" },
  ];

  if (isLoading) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-blue-600 mx-auto"></div>
            <p className="mt-4 text-slate-600">Loading tools...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <>
      {/* Tool Categories Section */}
      <section className="py-16 bg-white" id="tools">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
              Discover AI Automation Tools
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Browse our comprehensive collection of AI-powered automation tools and integrations designed to streamline your workflows and boost productivity.
            </p>
          </div>

          {/* Search and Filter Bar */}
          <div className="mb-12">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                <Input 
                  type="text" 
                  placeholder="Search tools and integrations..." 
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={activeFilter === category.id ? "default" : "secondary"}
                    size="sm"
                    onClick={() => setActiveFilter(category.id)}
                    className={activeFilter === category.id ? 
                      "bg-brand-blue-600 text-white hover:bg-brand-blue-700" : 
                      "bg-slate-100 text-slate-700 hover:bg-slate-200"
                    }
                  >
                    {category.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tools Grid Section */}
      <section className="pb-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredTools.length === 0 ? (
            <div className="text-center py-12">
              <i className="fas fa-search text-4xl text-slate-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-slate-900 mb-2">No tools found</h3>
              <p className="text-slate-600">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredTools.map((tool) => (
                <ToolCard key={tool.id} tool={tool} />
              ))}
            </div>
          )}

          {filteredTools.length > 0 && (
            <div className="text-center mt-12">
              <Button variant="secondary" className="bg-slate-100 hover:bg-slate-200 text-slate-700">
                Load More Tools <i className="fas fa-chevron-down ml-2"></i>
              </Button>
            </div>
          )}
        </div>
      </section>
    </>
  );
}
