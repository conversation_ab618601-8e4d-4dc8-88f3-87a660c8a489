import { z } from "zod";

// Video extraction request schema
export const videoExtractSchema = z.object({
  url: z.string().url("Please enter a valid URL"),
});

// Download request schema  
export const downloadRequestSchema = z.object({
  url: z.string().url(),
  formatId: z.string(),
});

export type VideoExtractRequest = z.infer<typeof videoExtractSchema>;
export type DownloadRequest = z.infer<typeof downloadRequestSchema>;

// Video format type
export type VideoFormat = {
  id?: number;
  videoId?: number;
  formatId: string;
  ext: string;
  quality: string;
  filesize: number | null;
  resolution: string | null;
  fps: number | null;
  vcodec: string | null;
  acodec: string | null;
};

// Video type
export type Video = {
  id: number;
  url: string;
  title: string;
  thumbnail: string;
  duration: number | null;
  platform: string;
  extractedAt?: Date;
};

// Combined video with formats for frontend
export type VideoWithFormats = Video & {
  formats: VideoFormat[];
};
