import { pgTable, text, serial, integer, boolean, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const videos = pgTable("videos", {
  id: serial("id").primaryKey(),
  url: text("url").notNull(),
  title: text("title").notNull(),
  thumbnail: text("thumbnail").notNull(),
  duration: integer("duration"),
  platform: text("platform").notNull(),
  extractedAt: timestamp("extracted_at").defaultNow(),
});

export const videoFormats = pgTable("video_formats", {
  id: serial("id").primaryKey(),
  videoId: integer("video_id").references(() => videos.id),
  formatId: text("format_id").notNull(),
  ext: text("ext").notNull(),
  quality: text("quality").notNull(),
  filesize: integer("filesize"),
  resolution: text("resolution"),
  fps: integer("fps"),
  vcodec: text("vcodec"),
  acodec: text("acodec"),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertVideoSchema = createInsertSchema(videos).omit({
  id: true,
  extractedAt: true,
});

export const insertVideoFormatSchema = createInsertSchema(videoFormats).omit({
  id: true,
});

// Video extraction request schema
export const videoExtractSchema = z.object({
  url: z.string().url("Please enter a valid URL"),
});

// Download request schema  
export const downloadRequestSchema = z.object({
  url: z.string().url(),
  formatId: z.string(),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type Video = typeof videos.$inferSelect;
export type InsertVideo = z.infer<typeof insertVideoSchema>;
export type VideoFormat = typeof videoFormats.$inferSelect;
export type InsertVideoFormat = z.infer<typeof insertVideoFormatSchema>;
export type VideoExtractRequest = z.infer<typeof videoExtractSchema>;
export type DownloadRequest = z.infer<typeof downloadRequestSchema>;

// Combined video with formats for frontend
export type VideoWithFormats = Video & {
  formats: VideoFormat[];
};
