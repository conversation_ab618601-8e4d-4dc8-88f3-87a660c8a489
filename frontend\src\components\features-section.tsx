export default function FeaturesSection() {
  const features = [
    {
      icon: "fas fa-lightning-bolt",
      title: "Lightning Fast Setup",
      description: "Get your automations running in minutes, not hours. Our intuitive interface makes complex workflows simple.",
      color: "bg-brand-blue-100 text-brand-blue-600"
    },
    {
      icon: "fas fa-shield-alt",
      title: "Enterprise Security",
      description: "Bank-level encryption and security protocols ensure your data and workflows are always protected.",
      color: "bg-green-100 text-green-600"
    },
    {
      icon: "fas fa-puzzle-piece",
      title: "500+ Integrations",
      description: "Connect with all your favorite tools and apps through our extensive integration library.",
      color: "bg-purple-100 text-purple-600"
    },
    {
      icon: "fas fa-brain",
      title: "AI-Powered Intelligence",
      description: "Our AI learns from your patterns and suggests optimizations to make your workflows even smarter.",
      color: "bg-orange-100 text-orange-600"
    },
    {
      icon: "fas fa-headset",
      title: "24/7 Expert Support",
      description: "Our automation experts are always available to help you optimize and troubleshoot your workflows.",
      color: "bg-indigo-100 text-indigo-600"
    },
    {
      icon: "fas fa-chart-line",
      title: "Advanced Analytics",
      description: "Track performance, identify bottlenecks, and measure ROI with comprehensive analytics dashboards.",
      color: "bg-teal-100 text-teal-600"
    }
  ];

  return (
    <section className="py-20 bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Why Choose Snapins.AI?
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Our platform combines cutting-edge AI technology with intuitive design to deliver unmatched automation capabilities.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              <div className={`w-16 h-16 ${feature.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                <i className={`${feature.icon} text-2xl`}></i>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">{feature.title}</h3>
              <p className="text-slate-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
