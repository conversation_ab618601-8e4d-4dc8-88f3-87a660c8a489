// Test script for PW Live functionality
const testUrl = "https://www.pw.live/watch/?batchSlug=67a06d84821c342d2453cdf4&batchSubjectId=67a1ef83c24f65d76bcbff81&subjectSlug=physics-691675&topicSlug=all&scheduleId=6835f1a2ebcc1972aff291e7&isUnderMaintenance=false&entryPoint=BATCH_LECTURE_VIDEOS_634fd383b08be600181ddd62&type=penpencilvdo&vType=BATCHES&learn2Earn=true&parentId=67a06d84821c342d2453cdf4&childId=6835f1a2ebcc1972aff291e7";

async function testPwLiveExtraction() {
  try {
    console.log('Testing PW Live video extraction...');
    
    const response = await fetch('http://localhost:5000/api/extract', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url: testUrl })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Extraction successful!');
    console.log('Title:', data.title);
    console.log('Platform:', data.platform);
    console.log('Formats available:', data.formats.length);
    console.log('Formats:', data.formats.map(f => `${f.quality} (${f.resolution})`));
    
    return data;
  } catch (error) {
    console.error('Extraction failed:', error.message);
    return null;
  }
}

// Run the test
testPwLiveExtraction();
