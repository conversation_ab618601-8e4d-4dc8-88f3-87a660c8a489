export default function TestimonialsSection() {
  const testimonials = [
    {
      rating: 5,
      content: "Snapins.AI transformed our workflow efficiency by 300%. The AI suggestions are incredibly smart and save us hours every day.",
      author: "<PERSON>",
      position: "VP of Operations, TechCorp"
    },
    {
      rating: 5,
      content: "The integration capabilities are outstanding. We connected 15 different tools seamlessly and automated our entire sales pipeline.",
      author: "<PERSON>",
      position: "Sales Director, GrowthLab"
    },
    {
      rating: 5,
      content: "Setup was incredibly fast and the support team helped us optimize our workflows. ROI was positive within the first month.",
      author: "<PERSON>",
      position: "COO, StartupXYZ"
    }
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: rating }, (_, i) => (
      <i key={i} className="fas fa-star text-yellow-400"></i>
    ));
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
            Trusted by Industry Leaders
          </h2>
          <p className="text-xl text-slate-600">
            See how teams are transforming their productivity with Snapins.AI
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-slate-50 rounded-xl p-8">
              <div className="flex items-center mb-4">
                <div className="flex">
                  {renderStars(testimonial.rating)}
                </div>
              </div>
              <p className="text-slate-700 mb-6">
                "{testimonial.content}"
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-slate-300 rounded-full mr-3"></div>
                <div>
                  <div className="font-semibold text-slate-900">{testimonial.author}</div>
                  <div className="text-sm text-slate-500">{testimonial.position}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
