export default function Footer() {
  return (
    <footer className="bg-slate-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <i className="fas fa-download text-2xl text-blue-400"></i>
              <span className="text-xl font-bold">Snap2Save</span>
            </div>
            <p className="text-slate-400 mb-4">
              Fast, secure, and easy-to-use video downloader for YouTube, Instagram, and Facebook.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Features</h3>
            <ul className="space-y-2 text-slate-400">
              <li>YouTube Downloads</li>
              <li>Instagram Videos</li>
              <li>Facebook Videos</li>
              <li>Multiple Formats</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Support</h3>
            <ul className="space-y-2 text-slate-400">
              <li>Help Center</li>
              <li>Contact Us</li>
              <li>Privacy Policy</li>
              <li>Terms of Service</li>
            </ul>
          </div>
        </div>
        <div className="border-t border-slate-800 mt-8 pt-8 text-center text-slate-400">
          <p>&copy; 2024 Snap2Save. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
