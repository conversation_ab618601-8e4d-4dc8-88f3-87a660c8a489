import { users, videos, videoFormats, type User, type InsertUser, type Video, type InsertVideo, type VideoFormat, type InsertVideoFormat, type VideoWithFormats } from "@shared/schema";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  saveVideo(video: InsertVideo, formats: InsertVideoFormat[]): Promise<VideoWithFormats>;
  getVideoByUrl(url: string): Promise<VideoWithFormats | undefined>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private videos: Map<number, Video>;
  private videoFormats: Map<number, VideoFormat>;
  private currentUserId: number;
  private currentVideoId: number;
  private currentFormatId: number;

  constructor() {
    this.users = new Map();
    this.videos = new Map();
    this.videoFormats = new Map();
    this.currentUserId = 1;
    this.currentVideoId = 1;
    this.currentFormatId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async saveVideo(insertVideo: InsertVideo, insertFormats: InsertVideoFormat[]): Promise<VideoWithFormats> {
    const videoId = this.currentVideoId++;
    const video: Video = { 
      ...insertVideo, 
      id: videoId,
      duration: insertVideo.duration ?? null,
      extractedAt: new Date()
    };
    this.videos.set(videoId, video);

    const formats: VideoFormat[] = insertFormats.map(format => {
      const formatId = this.currentFormatId++;
      const videoFormat: VideoFormat = {
        id: formatId,
        videoId,
        formatId: format.formatId,
        ext: format.ext,
        quality: format.quality,
        filesize: format.filesize ?? null,
        resolution: format.resolution ?? null,
        fps: format.fps ?? null,
        vcodec: format.vcodec ?? null,
        acodec: format.acodec ?? null
      };
      this.videoFormats.set(formatId, videoFormat);
      return videoFormat;
    });

    return {
      ...video,
      formats
    };
  }

  async getVideoByUrl(url: string): Promise<VideoWithFormats | undefined> {
    const video = Array.from(this.videos.values()).find(v => v.url === url);
    if (!video) return undefined;

    const formats = Array.from(this.videoFormats.values()).filter(
      format => format.videoId === video.id
    );

    return {
      ...video,
      formats
    };
  }
}

export const storage = new MemStorage();
