import { Button } from "@/components/ui/button";

export default function HeroSection() {
  return (
    <section className="bg-gradient-to-br from-slate-50 to-blue-50 py-20 lg:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-6">
            Automate Your Workflow with{" "}
            <span className="text-brand-blue-600">AI-Powered Tools</span>
          </h1>
          <p className="text-xl text-slate-600 mb-8 leading-relaxed">
            Connect your favorite apps, automate repetitive tasks, and boost productivity with our comprehensive suite of AI automation tools and seamless integrations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="bg-brand-blue-600 hover:bg-brand-blue-700 text-white px-8 py-4 text-lg shadow-lg hover:shadow-xl">
              Start Free Trial
            </Button>
            <Button size="lg" variant="outline" className="border-slate-300 hover:border-brand-blue-600 text-slate-700 hover:text-brand-blue-600 px-8 py-4 text-lg">
              Watch Demo
            </Button>
          </div>
          <div className="mt-12 flex items-center justify-center space-x-6 text-sm text-slate-500">
            <div className="flex items-center space-x-2">
              <i className="fas fa-check-circle text-green-500"></i>
              <span>No credit card required</span>
            </div>
            <div className="flex items-center space-x-2">
              <i className="fas fa-check-circle text-green-500"></i>
              <span>14-day free trial</span>
            </div>
            <div className="flex items-center space-x-2">
              <i className="fas fa-check-circle text-green-500"></i>
              <span>Cancel anytime</span>
            </div>
          </div>
        </div>
        
        <div className="mt-16">
          <img 
            src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&h=600" 
            alt="AI automation dashboard interface" 
            className="mx-auto rounded-xl shadow-2xl border border-slate-200 max-w-5xl w-full" 
          />
        </div>
      </div>
    </section>
  );
}
