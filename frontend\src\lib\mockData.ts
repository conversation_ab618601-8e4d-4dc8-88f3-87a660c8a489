import type { Tool } from "@shared/schema";

export const mockTools: Tool[] = [
  {
    id: 1,
    name: "AI Workflow Builder",
    description: "Create intelligent workflows that adapt and optimize automatically using machine learning algorithms.",
    category: "automation",
    icon: "fas fa-robot",
    color: "purple",
    features: ["AI-powered automation", "Real-time processing", "Advanced integrations", "Custom workflows"],
    platforms: ["Slack, Teams, Discord", "Google Workspace", "Microsoft 365", "Salesforce, HubSpot"],
    imageUrl: "https://images.unsplash.com/photo-1559028006-448665bd7c7f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=200",
    isPopular: true,
  },
  {
    id: 2,
    name: "Smart Data Sync",
    description: "Seamlessly integrate and synchronize data across multiple platforms with intelligent mapping.",
    category: "integration",
    icon: "fas fa-database",
    color: "green",
    features: ["Intelligent data mapping", "Real-time synchronization", "Multi-platform support", "Error handling"],
    platforms: ["Salesforce", "HubSpot", "Google Sheets", "Microsoft Excel"],
    imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=200",
    isPopular: false,
  },
  {
    id: 3,
    name: "AI Task Assistant",
    description: "Intelligent assistant that learns your patterns and automates routine tasks automatically.",
    category: "productivity",
    icon: "fas fa-brain",
    color: "blue",
    features: ["Pattern learning", "Task automation", "Smart suggestions", "Performance analytics"],
    platforms: ["Gmail", "Outlook", "Slack", "Trello"],
    imageUrl: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=200",
    isPopular: true,
  },
  {
    id: 4,
    name: "Predictive Analytics",
    description: "Advanced analytics platform that provides actionable insights and predicts workflow bottlenecks.",
    category: "analytics",
    icon: "fas fa-chart-line",
    color: "orange",
    features: ["Predictive modeling", "Real-time dashboards", "Custom reports", "Anomaly detection"],
    platforms: ["Tableau", "Power BI", "Google Analytics", "Mixpanel"],
    imageUrl: "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=200",
    isPopular: false,
  },
  {
    id: 5,
    name: "Unified Communication",
    description: "Centralize all team communications and automate notifications across multiple channels.",
    category: "integration",
    icon: "fas fa-comments",
    color: "indigo",
    features: ["Multi-channel support", "Automated notifications", "Message routing", "Team collaboration"],
    platforms: ["Slack", "Microsoft Teams", "Discord", "Email"],
    imageUrl: "https://images.unsplash.com/photo-**********-d307ca884978?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=200",
    isPopular: true,
  },
  {
    id: 6,
    name: "Smart Document AI",
    description: "Automatically process, categorize, and extract insights from documents using advanced AI.",
    category: "automation",
    icon: "fas fa-file-alt",
    color: "red",
    features: ["Document processing", "AI categorization", "Data extraction", "OCR technology"],
    platforms: ["Google Drive", "Dropbox", "SharePoint", "OneDrive"],
    imageUrl: "https://images.unsplash.com/photo-*************-b95a79798f07?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=200",
    isPopular: false,
  },
];
