{"version": 2, "builds": [{"src": "server/index.ts", "use": "@vercel/node", "config": {"includeFiles": ["client/**", "shared/**"]}}, {"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist/public"}}], "routes": [{"src": "/api/(.*)", "dest": "/server/index.ts"}, {"src": "/(.*)", "dest": "/server/index.ts"}], "env": {"NODE_ENV": "production"}, "functions": {"server/index.ts": {"maxDuration": 30}}}