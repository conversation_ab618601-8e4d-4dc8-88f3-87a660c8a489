import { useState } from "react";
import { <PERSON> } from "wouter";
import { But<PERSON> } from "@/components/ui/button";

export default function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <nav className="fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-slate-200 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-brand-blue-600 rounded-lg flex items-center justify-center">
                <i className="fas fa-bolt text-white text-sm"></i>
              </div>
              <span className="text-xl font-bold text-slate-900">Snap2Save</span>
            </Link>
          </div>
          
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <a href="#features" className="text-slate-700 hover:text-brand-blue-600 px-3 py-2 text-sm font-medium transition-colors">Features</a>
              <a href="#how-it-works" className="text-slate-700 hover:text-brand-blue-600 px-3 py-2 text-sm font-medium transition-colors">How it Works</a>
              <a href="#faq" className="text-slate-700 hover:text-brand-blue-600 px-3 py-2 text-sm font-medium transition-colors">FAQ</a>
            </div>
          </div>
          
          <div className="hidden md:flex items-center space-x-4">
            <Button className="bg-brand-blue-600 hover:bg-brand-blue-700 text-white">
              <i className="fas fa-download mr-2"></i>
              Start Downloading
            </Button>
          </div>
          
          <div className="md:hidden">
            <button 
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-slate-700 hover:text-brand-blue-600 p-2"
            >
              <i className="fas fa-bars text-lg"></i>
            </button>
          </div>
        </div>
      </div>
      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white border-t border-slate-200">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <a href="#features" className="block px-3 py-2 text-slate-700 hover:text-brand-blue-600 font-medium">Features</a>
            <a href="#how-it-works" className="block px-3 py-2 text-slate-700 hover:text-brand-blue-600 font-medium">How it Works</a>
            <a href="#faq" className="block px-3 py-2 text-slate-700 hover:text-brand-blue-600 font-medium">FAQ</a>
            <div className="pt-4 pb-3 border-t border-slate-200">
              <Button className="block w-full mt-2 bg-brand-blue-600 hover:bg-brand-blue-700 text-white px-3 py-2 rounded-lg font-medium">
                <i className="fas fa-download mr-2"></i>
                Start Downloading
              </Button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
