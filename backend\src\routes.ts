import type { Express, Response } from "express";
import { spawn } from "child_process";
import { storage } from "./storage.js";
import { videoExtractSchema, downloadRequestSchema, type VideoWithFormats } from "./types/index.js";

export function registerRoutes(app: Express): void {
  // Extract video metadata
  app.post("/api/extract", async (req, res) => {
    try {
      const { url } = videoExtractSchema.parse(req.body);

      // Check if video already exists in storage
      const existingVideo = await storage.getVideoByUrl(url);
      if (existingVideo) {
        return res.json(existingVideo);
      }

      // Extract video info using multiple methods
      const videoInfo = await extractVideoInfo(url);
      
      // Save to storage
      const savedVideo = await storage.saveVideo(
        {
          url,
          title: videoInfo.title,
          thumbnail: videoInfo.thumbnail,
          duration: videoInfo.duration,
          platform: getPlatform(url)
        },
        videoInfo.formats
      );

      res.json(savedVideo);
    } catch (error) {
      console.error("Extract error:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to extract video info" 
      });
    }
  });

  // Download video
  app.post("/api/download", async (req, res) => {
    try {
      const { url, formatId } = downloadRequestSchema.parse(req.body);

      // Get video info to find the title for filename
      const videoInfo = await storage.getVideoByUrl(url);
      if (!videoInfo) {
        return res.status(404).json({ message: "Video not found" });
      }

      const selectedFormat = videoInfo.formats.find(f => f.formatId === formatId);
      if (!selectedFormat) {
        return res.status(404).json({ message: "Format not found" });
      }

      // Generate safe filename
      const safeTitle = videoInfo.title.replace(/[^a-zA-Z0-9\s-_]/g, '').substring(0, 100);
      const filename = `${safeTitle}.${selectedFormat.ext}`;

      // Set response headers for download
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Transfer-Encoding', 'chunked');

      // Try yt-dlp first, then fallback to alternative methods
      try {
        await downloadWithYtDlp(url, formatId, res);
      } catch (error) {
        console.log('yt-dlp download failed, trying alternative method:', error);
        await downloadAlternative(url, formatId, res, videoInfo);
      }

    } catch (error) {
      console.error("Download error:", error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Failed to download video" 
      });
    }
  });
}

function getPlatform(url: string): string {
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return 'youtube';
  } else if (url.includes('instagram.com')) {
    return 'instagram';
  } else if (url.includes('facebook.com') || url.includes('fb.watch')) {
    return 'facebook';
  } else if (url.includes('pw.live')) {
    return 'pwlive';
  }
  return 'unknown';
}

async function extractVideoInfo(url: string): Promise<{
  title: string;
  thumbnail: string;
  duration: number;
  formats: Array<{
    formatId: string;
    ext: string;
    quality: string;
    filesize: number | null;
    resolution: string | null;
    fps: number | null;
    vcodec: string | null;
    acodec: string | null;
  }>;
}> {
  const platform = getPlatform(url);
  
  // Try yt-dlp first, then fallback to platform-specific methods
  try {
    return await extractWithYtDlp(url);
  } catch (error) {
    console.log('yt-dlp failed, trying alternative methods:', error);
    
    switch (platform) {
      case 'instagram':
        return await extractInstagramInfo(url);
      case 'youtube':
        return await extractYouTubeInfo(url);
      case 'facebook':
        return await extractFacebookInfo(url);
      case 'pwlive':
        return await extractPwLiveInfo(url);
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }
}

async function extractWithYtDlp(url: string): Promise<any> {
  return new Promise((resolve, reject) => {
    const ytdlp = spawn('python', [
      '-m', 'yt_dlp',
      '--dump-json',
      '--no-playlist',
      url
    ]);

    let jsonOutput = '';
    let errorOutput = '';

    ytdlp.stdout.on('data', (data) => {
      jsonOutput += data.toString();
    });

    ytdlp.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    ytdlp.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Failed to extract video info: ${errorOutput}`));
        return;
      }

      try {
        const videoData = JSON.parse(jsonOutput);
        
        const formats = (videoData.formats || [])
          .filter((f: any) => {
            // Prioritize formats with both video and audio
            return f.vcodec && f.vcodec !== 'none' && f.acodec && f.acodec !== 'none';
          })
          .map((f: any) => ({
            formatId: f.format_id,
            ext: f.ext || 'mp4',
            quality: f.format_note || f.quality || 'unknown',
            filesize: f.filesize || null,
            resolution: f.resolution || null,
            fps: f.fps || null,
            vcodec: f.vcodec || null,
            acodec: f.acodec || null
          }));

        // If no formats with both video and audio, add some common combined formats
        if (formats.length === 0) {
          formats.push(
            {
              formatId: 'best[height<=720]',
              ext: 'mp4',
              quality: 'Best 720p with audio',
              filesize: null,
              resolution: '720p',
              fps: null,
              vcodec: 'h264',
              acodec: 'aac'
            },
            {
              formatId: 'best[height<=480]',
              ext: 'mp4',
              quality: 'Best 480p with audio',
              filesize: null,
              resolution: '480p',
              fps: null,
              vcodec: 'h264',
              acodec: 'aac'
            },
            {
              formatId: 'best',
              ext: 'mp4',
              quality: 'Best quality with audio',
              filesize: null,
              resolution: 'Best',
              fps: null,
              vcodec: 'h264',
              acodec: 'aac'
            }
          );
        }

        resolve({
          title: videoData.title || 'Unknown Title',
          thumbnail: videoData.thumbnail || '',
          duration: videoData.duration || 0,
          formats
        });
      } catch (error) {
        reject(new Error(`Failed to parse video info: ${error}`));
      }
    });

    ytdlp.on('error', (error) => {
      reject(new Error(`yt-dlp process error: ${error.message}`));
    });
  });
}

async function extractPwLiveInfo(url: string): Promise<any> {
  // pw.live extraction - yt-dlp should handle this, but provide fallback
  try {
    // Extract video ID from pw.live URL
    const videoIdMatch = url.match(/pw\.live\/([A-Za-z0-9_-]+)/);
    if (!videoIdMatch) {
      throw new Error('Invalid pw.live URL format');
    }
    
    const videoId = videoIdMatch[1];
    
    // Mock data for demonstration - in production, yt-dlp should handle pw.live
    return {
      title: `PW Live Video ${videoId}`,
      thumbnail: 'https://via.placeholder.com/640x360?text=PW+Live+Video',
      duration: 120,
      formats: [
        {
          formatId: 'best',
          ext: 'mp4',
          quality: 'Best quality with audio',
          filesize: 10485760, // 10MB
          resolution: '720p',
          fps: 30,
          vcodec: 'h264',
          acodec: 'aac'
        },
        {
          formatId: 'worst',
          ext: 'mp4',
          quality: 'Lower quality with audio',
          filesize: 5242880, // 5MB
          resolution: '480p',
          fps: 30,
          vcodec: 'h264',
          acodec: 'aac'
        }
      ]
    };
  } catch (error) {
    throw new Error(`PW Live extraction failed: ${error}`);
  }
}

async function extractInstagramInfo(url: string): Promise<any> {
  // For now, provide a mock response for Instagram
  // In production, you'd use a proper Instagram API or service
  try {
    // Extract shortcode from URL
    const shortcodeMatch = url.match(/\/p\/([A-Za-z0-9_-]+)/);
    if (!shortcodeMatch) {
      throw new Error('Invalid Instagram URL format');
    }

    const shortcode = shortcodeMatch[1];

    // Mock data for demonstration
    return {
      title: `Instagram Post ${shortcode}`,
      thumbnail: 'https://via.placeholder.com/640x640?text=Instagram+Video',
      duration: 30,
      formats: [
        {
          formatId: 'video_hd',
          ext: 'mp4',
          quality: 'HD',
          filesize: 5242880, // 5MB
          resolution: '720x720',
          fps: 30,
          vcodec: 'h264',
          acodec: 'aac'
        },
        {
          formatId: 'video_sd',
          ext: 'mp4',
          quality: 'SD',
          filesize: 2621440, // 2.5MB
          resolution: '480x480',
          fps: 30,
          vcodec: 'h264',
          acodec: 'aac'
        }
      ]
    };
  } catch (error) {
    throw new Error(`Instagram extraction failed: ${error}`);
  }
}

async function extractYouTubeInfo(url: string): Promise<any> {
  // YouTube extraction - simplified version
  // In production, you'd want to use YouTube Data API
  throw new Error('YouTube extraction not implemented yet. Please install yt-dlp.');
}

async function extractFacebookInfo(url: string): Promise<any> {
  // Facebook extraction - simplified version
  throw new Error('Facebook extraction not implemented yet. Please install yt-dlp.');
}

async function downloadWithYtDlp(url: string, formatId: string, res: Response): Promise<void> {
  return new Promise((resolve, reject) => {
    // Ensure we get both video and audio by using format selection that merges them
    let format = formatId;

    // If it's a simple format ID, make sure we get audio too
    if (!formatId.includes('[') && !formatId.includes('best') && !formatId.includes('worst')) {
      format = `${formatId}+bestaudio/best[height<=720]/best`;
    }

    const ytdlp = spawn('python', [
      '-m', 'yt_dlp',
      '--format', format,
      '--merge-output-format', 'mp4',
      '--output', '-',
      url
    ]);

    ytdlp.stdout.pipe(res);

    ytdlp.stderr.on('data', (data) => {
      console.error('yt-dlp stderr:', data.toString());
    });

    ytdlp.on('close', (code) => {
      if (code !== 0) {
        console.error(`yt-dlp process exited with code ${code}`);
        reject(new Error(`yt-dlp process exited with code ${code}`));
      } else {
        res.end();
        resolve();
      }
    });

    ytdlp.on('error', (error) => {
      console.error('yt-dlp error:', error);
      reject(error);
    });
  });
}

async function downloadAlternative(url: string, formatId: string, res: Response, videoInfo: any): Promise<void> {
  // For demo purposes, create a small sample video file
  const platform = getPlatform(url);

  if (platform === 'instagram' || platform === 'pwlive') {
    // Create a simple text file as a demo download
    const content = `${platform.toUpperCase()} Video Download Demo\n\nURL: ${url}\nFormat: ${formatId}\nTitle: ${videoInfo.title}\n\nThis is a demo file. In production, this would be the actual video content.`;

    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Length', Buffer.byteLength(content));
    res.write(content);
    res.end();
  } else {
    throw new Error(`Alternative download not implemented for ${platform}`);
  }
}
